import ReactDOM from 'react-dom/client'
import App from '@/App'
import eruda from 'eruda'
// import * as Sentry from '@sentry/react'
import './configs/locales/index'
import { isIPad, isPad, isIphone, isPhone, isMachine, isWebApp } from './utils'

// 机器中的测试环境，打开调试
if (
  window.location.hostname === 'mirror_test.mgru.info' ||
  window.location.hostname === 'mirror.wujiebantu.com' ||
  (/:16661/.test(window.location.href) &&
    (isIPad() ||
      isPad() ||
      isIphone() ||
      isPhone() ||
      isMachine() ||
      isWebApp()))
) {
  eruda.init()
}

// Sentry.init({
//   dsn: 'https://<EMAIL>/7',
//   integrations: [
//     Sentry.browserTracingIntegration(),
//     Sentry.replayIntegration(),
//   ],
//   // Performance Monitoring
//   tracesSampleRate: 1.0, //  Capture 100% of the transactions
//   // Session Replay
//   replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
//   replaysOnErrorSampleRate: 1.0, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
// })

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement)
root.render(
  /** 关闭严格模式，因为develop下会渲染两次 */
  // <React.StrictMode>
  <App />
  // </React.StrictMode>
)
