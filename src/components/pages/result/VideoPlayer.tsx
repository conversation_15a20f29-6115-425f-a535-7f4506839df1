import { MyImage } from '@/components/ui/MyImage'
import { useEffect, useRef, useState } from 'react'

interface Props {
  poster: string
  videoUrl: string
}

export function VideoPlayer({ poster, videoUrl }: Props) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [playing, setPlaying] = useState(true)

  useEffect(() => {
    videoRef.current?.addEventListener('playing', () => {
      console.log('paying')
      setPlaying(true)
    })
    videoRef.current?.addEventListener('ended', () => {
      console.log('end')
      setPlaying(false)
    })
    return () => {
      videoRef.current?.removeEventListener('playing', () => {
        setPlaying(true)
      })
      videoRef.current?.removeEventListener('ended', () => {
        setPlaying(false)
      })
    }
  }, [videoUrl, videoRef.current])

  return (
    <>
      {playing ? (
        <video ref={videoRef} src={videoUrl} autoPlay poster={poster} />
      ) : (
        <>
          <MyImage
            src={poster}
            className="w-full h-full rounded-2xl"
            isAppCache={false}
          />
          <div
            className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2  w-[56px] h-[56px]"
            onClick={() => {
              setPlaying(true)
              videoRef.current?.play()
            }}
          >
            <img
              src="/images/video/play-icon.png"
              className="w-full h-full"
              alt=""
            />
          </div>
        </>
      )}
    </>
  )
}
