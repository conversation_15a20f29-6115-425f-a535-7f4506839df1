import classnames from 'classnames'
import styles from './PictureResult.module.css'
import { useAtom } from 'jotai'
import { mazeResultImagesAtom, screenOrientationAtom } from '@/stores'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useState, useMemo, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/shad/button'
import { MazePictureResultItem } from './MazePictureResultItem'
import { MirrorAiTaskStatus } from '@/graphqls/types'
import { MirrorPicture } from 'wujieai-react-icon'
import classNames from 'classnames'
import { Swiper as SwiperType } from 'swiper/types'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Grid } from 'swiper/modules'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/grid'

import Icon_checked from '/images/icons/checked.svg'
import Icon_right from '/images/icons/arrow-right.svg'
import Icon_left from '/images/icons/arrow-left.svg'
import { isIPad, isPhone } from '@/utils'

const SwiperNextButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute right-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slideNext()}
    >
      <img src={Icon_right} alt="Next" className="w-24 h-24" />
    </div>
  )
}

const SwiperPrevButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute left-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slidePrev()}
    >
      <img src={Icon_left} alt="Previous" className="w-24 h-24" />
    </div>
  )
}

export const MazePictureResult = ({
  setBackOpen,
}: {
  setBackOpen: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const swiperRef = useRef<SwiperType | null>(null)
  const navigate = useNavigate()
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)
  // Store selected image IDs with their taskBaseId
  const [selectedImages, setSelectedImages] = useState<{
    [taskBaseId: string]: number[]
  }>({})
  const [resultImages, setResultImages] = useAtom(mazeResultImagesAtom)
  const [screenOrientation] = useAtom(screenOrientationAtom)

  // Animation states
  const [animatingImage, setAnimatingImage] = useState<string | null>(null)
  const [clonedElements, setClonedElements] = useState<{
    [key: string]: HTMLElement | null
  }>({})
  const buttonRef = useRef<HTMLButtonElement>(null)
  const cameraIconRef = useRef<HTMLSpanElement>(null)

  const { t } = useTranslation()
  console.log('setBackOpen', setBackOpen)

  const [searchParams] = useSearchParams()

  useEffect(() => {
    return () => {
      setResultImages({})
    }
  }, [])

  useEffect(() => {
    const swiper = swiperRef.current
    if (swiper) {
      // Update navigation state on slide change
      swiper.on('slideChange', () => {
        setIsBeginning(swiper.isBeginning)
        setIsEnd(swiper.isEnd)
      })

      // Initialize states
      setIsBeginning(swiper.isBeginning)
      setIsEnd(swiper.isEnd)
    }
  }, [swiperRef.current])

  const isAllTaskComplete = useMemo(() => {
    return Object.values(resultImages).every(
      it => it?.[0]?.status === MirrorAiTaskStatus.SUCCESS
    )
  }, [resultImages])

  const currentTaskCompleteCount = useMemo(() => {
    console.log('result images', resultImages)
    return Object.values(resultImages).reduce((acc, it) => {
      if (it?.[0]?.status === MirrorAiTaskStatus.SUCCESS) {
        return acc + 1
      }
      return acc
    }, 0)
  }, [resultImages])

  const taskBaseIds = searchParams.get('taskBaseId')?.split(',')
  console.log('taskBaseId', taskBaseIds)

  // Function to create and animate a clone of the selected image
  const animateImageToCart = (
    imageElement: HTMLElement,
    taskBaseId: string,
    imageId: number
  ) => {
    // Only add animation if not already selected
    const currentSelected = selectedImages[taskBaseId] || []
    if (currentSelected.includes(imageId)) {
      // If already selected, just remove it without animation
      setSelectedImages(prev => ({
        ...prev,
        [taskBaseId]: prev[taskBaseId]?.filter(id => id !== imageId) || [],
      }))
      return
    }

    // Create a clone of the image for animation
    const clone = imageElement.cloneNode(true) as HTMLElement
    const rect = imageElement.getBoundingClientRect()

    // Set initial position and style
    clone.style.position = 'fixed'
    clone.style.left = `${rect.left}px`
    clone.style.top = `${rect.top}px`
    clone.style.width = `${rect.width}px`
    clone.style.height = `${rect.height}px`
    clone.style.zIndex = '1000'
    clone.style.pointerEvents = 'none'

    // Add clone to the body
    document.body.appendChild(clone)

    // Get camera icon position for animation target
    const cameraRect = cameraIconRef.current?.getBoundingClientRect()

    // Create a unique key for this animation
    const animationKey = `${taskBaseId}-${imageId}-${Date.now()}`
    setAnimatingImage(animationKey)

    // Store the clone element
    setClonedElements(prev => ({
      ...prev,
      [animationKey]: clone,
    }))

    // Add to selected images immediately
    setSelectedImages(prev => ({
      ...prev,
      [taskBaseId]: [...(prev[taskBaseId] || []), imageId],
    }))

    // Animation in two steps for better control
    // Step 1: Scale up slightly
    setTimeout(() => {
      clone.style.transition =
        'transform 0.2s cubic-bezier(0.25, 0.1, 0.25, 1.0), opacity 0.2s ease'
      clone.style.transform = 'scale(1.1)'

      // Step 2: Move to camera and fade out
      setTimeout(() => {
        if (cameraRect) {
          clone.style.transition = 'all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0)'
          // Add a slight rotation for a more dynamic effect
          clone.style.transform = `scale(0.1) rotate(${Math.random() > 0.5 ? 10 : -10}deg)`
          clone.style.left = `${cameraRect.left + cameraRect.width / 2 - rect.width * 0.32}px`
          clone.style.top = `${cameraRect.top + cameraRect.height / 2 - rect.height * 0.38}px`
          clone.style.opacity = '0' // Fade out completely when reaching the camera
          clone.style.borderRadius = '50%'
          // Add a box shadow for a 3D effect
          clone.style.boxShadow = '0 5px 15px rgba(0,0,0,0.3)'
        }

        // Remove clone after animation completes
        setTimeout(() => {
          document.body.removeChild(clone)
          setClonedElements(prev => {
            const newState = { ...prev }
            delete newState[animationKey]
            return newState
          })
          setAnimatingImage(null)
        }, 400) // Match the duration of the second animation
      }, 200) // Match the duration of the first animation
    }, 50)
  }

  // Handle image selection/deselection
  const handleImageSelection = (
    taskBaseId: string,
    imageId: number,
    imageElement: HTMLElement
  ) => {
    animateImageToCart(imageElement, taskBaseId, imageId)
  }

  // Navigate to detail page with selected image IDs
  const navToDetailPage = async () => {
    // Flatten all selected images into a format for URL params
    const allSelectedImages = Object.entries(selectedImages)
      .filter(([, ids]) => ids.length > 0)
      .map(([taskId, imageIds]) => ({ taskId, imageIds }))

    if (allSelectedImages.length === 0) {
      // If no images selected, show a message or select the first available image
      console.log('No images selected')
      return
    }

    // Format: /detail?images=taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
    const imagesParam = allSelectedImages
      .map(({ taskId, imageIds }) => `${taskId}:${imageIds.join(',')}`)
      .join(';')

    navigate(`/detail?images=${imagesParam}`)
  }

  console.log('result', resultImages, screenOrientation)

  const multiline = screenOrientation.isPortrait
  // Default Swiper parameters
  const defaultSwiperProps = useMemo(
    () => ({
      // There are many special requirements and they are not universal. Make modifications with caution
      slidesPerView: multiline
        ? isPhone()
          ? 1.32 // iphone 显示1.32个图片
          : taskBaseIds?.length && taskBaseIds?.length > 4 // 4张要求居中，否则竖屏按照2。66个显示
            ? 2.66
            : 2
        : 4, // 横屏则展示4个
      spaceBetween: isIPad() ? 28 : isPhone() ? 16 : 40,
      loop: false,
      pagination: false,
      modules: [Grid],
      className: 'mySwiper',
      initialSlide: 0,
      centeredSlides: isPhone() ? true : false,
    }),
    [multiline, taskBaseIds?.length]
  )

  console.log('isP', isPhone())

  return (
    <>
      <div className="flex items-center justify-center h-full w-full flex-col">
        <h1 className="maze-page-title pb-8">
          {isAllTaskComplete
            ? t('选择喜欢的照片（可多选）')
            : t('图片正在生成', {
                completed: currentTaskCompleteCount,
                all: taskBaseIds?.length,
              })}
        </h1>
        <div
          className={classNames(
            'py-10 w-[92vw] h-auto overflow-hidden',
            screenOrientation.isLandScape
              ? ''
              : 'px-20 h-auto swiper-overflow-visible ipad:px-12 ipad:py-6 ipad:w-[80vw] ipad:h-auto phone:w-full phone:h-auto phone:px-0 phone:no-swiper-overflow-visible',
            screenOrientation.isPortrait &&
              taskBaseIds?.length &&
              taskBaseIds?.length > 4
              ? 'ipad:px-0 ipad:w-full'
              : ''
          )}
        >
          {taskBaseIds &&
            taskBaseIds.length > (multiline && !isPhone() ? 4 : 2) && (
              <>
                <div
                  className={classNames({
                    'hidden pointer-events-none': isBeginning,
                  })}
                >
                  <SwiperPrevButton swiperRef={swiperRef} />
                </div>
                <div
                  className={classNames({
                    'hidden pointer-events-none': isEnd,
                  })}
                >
                  <SwiperNextButton swiperRef={swiperRef} />
                </div>
              </>
            )}
          <Swiper
            {...defaultSwiperProps}
            onSwiper={(swiper: any) => {
              swiperRef.current = swiper
            }}
            grid={{ rows: multiline && !isPhone() ? 2 : 1, fill: 'row' }}
          >
            {taskBaseIds?.map((taskBaseId, index) => {
              // Get the images for this taskBaseId
              const taskImages = resultImages[Number(taskBaseId)] || []
              // Get the selected image IDs for this taskBaseId
              const selectedIds = selectedImages[taskBaseId] || []

              return (
                <SwiperSlide
                  key={index}
                  onClick={e => {
                    if (
                      taskImages.length > 0 &&
                      taskImages[0].status === MirrorAiTaskStatus.SUCCESS
                    ) {
                      // Get the image element for animation
                      const imageElement = e.currentTarget
                      // Toggle selection of the first image with animation
                      handleImageSelection(
                        taskBaseId,
                        taskImages[0].id,
                        imageElement
                      )
                    }
                  }}
                  className={classnames([
                    'cursor-pointer transition-all rounded-2xl relative bg-black flex-shrink-0',
                    // selectedIds.length > 0 ? 'maze-box-shadow' : '',
                  ])}
                >
                  {() => (
                    <>
                      <MazePictureResultItem taskBaseId={Number(taskBaseId)} />
                      {/* Selection indicator */}
                      {selectedIds.length > 0 && (
                        <div className="absolute inset-0 flex justify-center items-center w-full h-full rounded-2xl backdrop-blur-sm bg-black bg-opacity-35">
                          <img className="phone:w-32" src={Icon_checked} />
                        </div>
                      )}
                    </>
                  )}
                </SwiperSlide>
              )
            })}
          </Swiper>
        </div>
        <div className="pt-8">
          <Button
            ref={buttonRef}
            size="lg"
            className={classNames(
              'flex maze-bg-gradient-btn w-[29.32rem] h-[9.375rem] text-[3.125rem] rounded-[150px] ',
              'ipad:w-[24rem] ipad:h-[8rem] ipad:text-[2.5rem]'
            )}
            variant="outline"
            onClick={() => navToDetailPage()}
            disabled={
              !isAllTaskComplete ||
              Object.values(selectedImages).every(ids => ids.length === 0)
            }
          >
            {t('下一步')}
            {Object.values(selectedImages).flat().length > 0 && (
              <>
                <span className="inline-block" ref={cameraIconRef}>
                  <MirrorPicture
                    size={70}
                    className="btn-text-color ml-4 phone:w-20 phone:h-20"
                  />
                </span>
                <span className="mx-2">
                  {Object.values(selectedImages).flat().length}/
                  {taskBaseIds?.length}
                </span>
              </>
            )}
          </Button>
        </div>
      </div>
    </>
  )
}
