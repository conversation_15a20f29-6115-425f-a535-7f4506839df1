import { MyImage } from '@/components/ui/MyImage'
import { MirrorAiTaskStatus } from '@/graphqls/types'
import { MyMirrorAiTask } from '@/stores/types'
import { cn } from '@/utils/shad'
import { ThreeView } from '../3d/ThreeView'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { publicPreLoadSourceObj } from '@/configs/source'

interface Props {
  curImg?: MyMirrorAiTask
  imgClassName?: string
}

export function MainImage({ curImg, imgClassName }: Props) {
  const [focus3d, setFocues3d] = useState(false)
  const { t } = useTranslation()

  useEffect(() => {
    setFocues3d(false)
  }, [curImg?.id])

  // return (
  //   <div
  //     className={cn(
  //       'w-full flex justify-center flex-col text-center rounded-2xl text-[1.5rem] font-bold leading-[2rem]'
  //     )}
  //   >
  //     <div className="aspect-[2/3] flex flex-col items-center justify-center gap-16">
  //       {/* 排队中与生成中合并，统一展示进度条 */}
  //       <CDNImage
  //         src={publicPreLoadSourceObj.painting}
  //         className="w-[18.75rem] h-[18.75rem] phone:w-full phone:h-auto block mx-auto"
  //         alt=""
  //       />
  //       <div className="mb-2 text-white phone:text-[2.5rem]">
  //         {Math.floor((curImg?.generatingCompletePercent || 0) * 100)}%
  //       </div>
  //     </div>
  //   </div>
  // )

  return (
    <div
      className={cn(
        'w-full flex justify-center flex-col text-center rounded-2xl text-[1.5rem] font-bold leading-[2rem]'
      )}
    >
      {curImg?.status === MirrorAiTaskStatus.SUCCESS ? (
        <div className="w-full h-full relative">
          {!(!!curImg.threeDModelingInfo && focus3d) && (
            <MyImage
              src={curImg?.editResultUrls?.[2] ?? curImg?.resultUrl}
              tag="v800"
              className="w-full h-full rounded-2xl"
              isAppCache={false}
              imgClassName={imgClassName}
            />
          )}
          {!!curImg.threeDModelingInfo && focus3d && (
            <div className="w-full h-full rounded-2xl">
              <ThreeView url={curImg.threeDModelingInfo.modelUrl || ''} />
            </div>
          )}
          {!!curImg.threeDModelingInfo && (
            <div className="absolute bottom-6 gap-4 flex items-center justify-center w-full cursor-pointer text-base">
              <div
                className={cn(
                  'rounded-full bg-neutral-900 text-neutral-500 border border-solid border-neutral-600 w-[86px] h-10 flex items-center justify-center',
                  {
                    'text-neutral-300': !focus3d,
                  }
                )}
                onClick={() => setFocues3d(false)}
              >
                {t('原图')}
              </div>
              <div
                className={cn(
                  'rounded-full bg-neutral-900 text-neutral-500  border border-solid border-neutral-600 w-[86px] h-10 flex items-center justify-center',
                  {
                    'text-neutral-300': focus3d,
                  }
                )}
                onClick={() => setFocues3d(true)}
              >
                {t('3D效果')}
              </div>
            </div>
          )}
        </div>
      ) : curImg?.status === MirrorAiTaskStatus.FAIL ? (
        <>
          <div className="mb-2">
            <img
              src="/images/common/<EMAIL>"
              className="w-[64px] h-[64px]"
              alt=""
            />
          </div>
          <div>
            {t('生成失败')}
            <br />
            {t('请稍后重试')}
          </div>
        </>
      ) : (
        <div className="aspect-[2/3] flex flex-col items-center justify-center gap-16">
          {/* 排队中与生成中合并，统一展示进度条 */}
          <img
            src={publicPreLoadSourceObj.painting}
            className="w-[18.75rem] h-[18.75rem] phone:w-full phone:h-auto block mx-auto"
            alt=""
          />
          <div className="mb-2 text-white phone:text-[2.5rem]">
            {Math.floor((curImg?.generatingCompletePercent || 0) * 100)}%
          </div>
        </div>
      )}
    </div>
  )
}
