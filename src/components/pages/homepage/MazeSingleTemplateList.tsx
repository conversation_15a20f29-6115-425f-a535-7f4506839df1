import { useEffect, useMemo, useRef, useState } from 'react'
import MazeSingleTemplate from './MazeSingleTemplate'
import { Swiper as SwiperType } from 'swiper/types'
import { TemplateListProps } from './const'
import classNames from 'classnames'

import { Swiper, SwiperSlide } from 'swiper/react'
import { Grid, Navigation } from 'swiper/modules'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/grid'
import 'swiper/css/pagination'
import 'swiper/css/autoplay'
import { ThemeDetail } from '@/apis/types'

import Icon_right from '/images/icons/arrow-right.svg'
import Icon_left from '/images/icons/arrow-left.svg'
import { isPhone } from '@/utils'

// Custom navigation button components
const SwiperNextButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute right-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slideNext()}
    >
      <img src={Icon_right} alt="Next" className="w-24 h-24" />
    </div>
  )
}

const SwiperPrevButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute left-6 top-1/2 -translate-y-1/2 z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slidePrev()}
    >
      <img src={Icon_left} alt="Previous" className="w-24 h-24" />
    </div>
  )
}

/** 单行模版列表 */
const MazeSingleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  multiline,
  listKey,
  swiperProps = {}, // Default to empty object if not provided
  activeGender,
}: TemplateListProps) => {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)

  console.log('listKey', listKey)

  const list = useMemo(() => {
    if (multiline && selectTemplateList.length % 4 !== 0) {
      const len = 4 - (selectTemplateList.length % 4)
      return selectTemplateList.concat(selectTemplateList.slice(0, len))
    } else {
      return selectTemplateList
    }
  }, [selectTemplateList])

  useEffect(() => {
    const swiper = swiperRef.current
    if (swiper) {
      // Update navigation state on slide change
      swiper.on('slideChange', () => {
        console.log('slideChange', swiper.activeIndex)
        setIsBeginning(swiper.isBeginning)
        setIsEnd(swiper.isEnd)

        // Trigger setActiveTemplate when slide changes
        const activeIndex = swiper.activeIndex
        if (list[activeIndex]) {
          setActiveTemplate(list[activeIndex])
        }
      })

      // Initialize states
      setIsBeginning(swiper.isBeginning)
      setIsEnd(swiper.isEnd)
    }
  }, [swiperRef.current, list, setActiveTemplate])

  console.log('MazeSingleTemplateList', {
    listLength: list.length,
    customProps: swiperProps,
  })

  // Default Swiper parameters
  const defaultSwiperProps = useMemo(
    () => ({
      slidesPerView: isPhone() ? 1.32 : 4,
      spaceBetween: 0,
      loop: multiline ? false : true,
      pagination: false,
      navigation: false, // Disable default navigation since we're using custom buttons
      centeredSlides: true,
      modules: [Grid, Navigation],
      className: 'mySwiper',
      initialSlide: 0,
    }),
    [multiline]
  )

  // Merge default props with custom props, with custom props taking precedence
  const mergedSwiperProps = useMemo(
    () => ({
      ...defaultSwiperProps,
      ...swiperProps,
    }),
    [defaultSwiperProps, swiperProps]
  )

  return (
    <div
      className={classNames(
        'w-full relative py-6 top-[50%] -translate-y-[50%]'
      )}
    >
      {/* Custom navigation buttons - only show when there are enough slides */}
      {list.length > (multiline ? 4 : 2) && (
        <>
          <div
            className={classNames({
              'hidden pointer-events-none': isBeginning,
            })}
          >
            <SwiperPrevButton swiperRef={swiperRef} />
          </div>
          <div
            className={classNames({
              'hidden pointer-events-none': isEnd,
            })}
          >
            <SwiperNextButton swiperRef={swiperRef} />
          </div>
        </>
      )}

      <Swiper
        grid={{ rows: multiline ? 2 : 1, fill: 'row' }}
        {...mergedSwiperProps}
        onSwiper={(swiper: any) => {
          swiperRef.current = swiper
        }}
      >
        {list.map((it, i) => (
          <SwiperSlide
            key={i}
            onClick={() => {
              console.log('index', i)
              if (swiperRef.current) {
                swiperRef.current[multiline ? 'slideTo' : 'slideToLoop'](i)
              }
            }}
          >
            {({ isActive }) => (
              <MazeSingleTemplate
                activeGender={activeGender}
                key={i}
                item={it as ThemeDetail}
                active={isActive || it?.id === activeTemplate?.id}
                onSelect={() => setActiveTemplate(it)}
                className={classNames(
                  'transition-transform duration-300 rounded-[3rem] ipad:rounded-[2rem] ',
                  {
                    'active scale-[1] opacity-100': isActive,
                    'scale-[0.9] opacity-80': !isActive,
                  }
                )}
              />
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  )
}
export default MazeSingleTemplateList
