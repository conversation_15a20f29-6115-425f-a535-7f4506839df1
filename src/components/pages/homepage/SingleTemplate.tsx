import { MirrorSexEnum } from '@/graphqls/types'
import classnames from 'classnames'
import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'
import styles from './SingleTemplate.module.css'
import { ThemeDetail } from '@/apis/types'
import { useAtomValue, useSetAtom } from 'jotai'
import { screenOrientationAtom, isShowThemeDetailModalAtom } from '@/stores'

/** 单个模版 */
function SingleTemplate({
  item,
  active = false,
  onSelect,
  isMultiple = false,
  className,
  activeGender,
}: {
  item: ThemeDetail
  active: boolean
  isMultiple?: boolean
  className?: string
  activeGender?: string
  onSelect: () => void
}) {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const setThemeDetailModalOpen = useSetAtom(isShowThemeDetailModalAtom)
  console.log('singleTemplate:', item)
  return (
    <div
      className={classnames(
        className,
        styles.template,
        isMultiple && styles.multiple,
        // 'flex flex-col cursor-pointer',
        {
          [styles.active]: active,
        }
      )}
      onClick={onSelect}
    >
      {item.female_model_count || item.male_model_count ? (
        <div
          onClick={() => setThemeDetailModalOpen(true)}
          className="absolute cursor-pointer bg-opacity-50 text-[2.5rem] bg-black text-white leading-none left-8 bottom-8 rounded-xl px-4 py-2 flex items-center justify-center ipad:scale-[0.8] ipad:left-4 ipad:bottom-4 maze-badge-default"
        >
          <span className="px-1">×</span>
          <span>
            {activeGender === MirrorSexEnum.FEMALE
              ? item.female_model_count
              : item.male_model_count}
          </span>
        </div>
      ) : (
        <div className="absolute no-data-found"></div>
      )}
      <MyImage
        src={
          activeGender === MirrorSexEnum.FEMALE
            ? item?.cover_image_female
            : item?.cover_image
        }
        tag="v800"
        className={classNames('rounded-[3rem] ipad:rounded-[2rem] ', [
          screenOrientation.isLandScape
            ? 'h-[12.23vh]'
            : 'h-[36.28vh] ipad:h-[35vh] phone:h-[36.28dvh]',
        ])}
        imgClassName="object-cover"
      />
      {/* <div
        className={classnames(
          'font-bold text-xl mt-4 line-clamp-1 text-center',
          {
            'text-neutral-50': !active,
            'text-gradient-primary': active,
          }
        )}
      >
        {item?.name}
      </div> */}
    </div>
  )
}

export default SingleTemplate
