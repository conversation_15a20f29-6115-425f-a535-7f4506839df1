import { Button } from '@/components/ui/shad/button'
import React, { useEffect, useRef } from 'react'
import { fenToYuan, graphQLErrorMessage, toCDNImage } from '@/utils'
import classNames from 'classnames'
import QRCode from 'qrcode.react'
import { useCountDown } from '@/hooks/useCountDown'
import { MyModal } from '@/components/ui/MyModal'
import {
  GetMirrorDrawOrderDocument,
  GetMirrorDrawOrderQuery,
  GetMirrorDrawOrderQueryVariables,
  DrawItemFragment,
  OrderStatus,
  useCancelOrderMutation,
  AiTaskType,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useNavigate } from 'react-router-dom'
import { publicPreLoadSourceObj } from '@/configs/source'
import { usePayOrder } from '@/hooks/usePayOrder'
import { useToast } from '@/components/ui/shad/use-toast'
import { useAtom, useAtomValue } from 'jotai'
import {
  canShowVideoAtom,
  drawPayOrderAtom,
  imageNumAtom,
  machineInfoAtom,
  printerEnableAtom,
  screenOrientationAtom,
  taskTypeAtom,
} from '@/stores'
import { POLL_TICK, ORDER_EXPIRE_TIME, ORDER_TYPE } from '@/configs'
import { useValidResourceChange } from '@/hooks/useValidResourceChange'
import { useDebounce } from '@/hooks/useDebounce'
import { MyImage } from '@/components/ui/MyImage'
import { CDNImage } from '@/components/ui/CDNImage'
import { PrinterStatus } from '@/stores/types'

const uri = import.meta.env.VITE_MOBILE_HOST

const ModelPaymentModal: React.FC<{
  visible: boolean
  setVisible: React.Dispatch<React.SetStateAction<boolean>>
  onSuccess?: () => void
  activeTemplate: DrawItemFragment | undefined | null
  printPrice: number
  isPortrait?: boolean
}> = ({
  visible,
  setVisible,
  activeTemplate,
  printPrice,
  onSuccess,
  isPortrait,
}) => {
  const [payInfo] = useAtom(drawPayOrderAtom)
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const [imageNum] = useAtom(imageNumAtom)
  const [taskType] = useAtom(taskTypeAtom)
  const [, setCanShowVideo] = useAtom(canShowVideoAtom)

  const { createDrawPayOrder } = usePayOrder()
  const { toast } = useToast()
  const navigator = useNavigate()
  const { second, resetSecond, clearSecond } = useCountDown(
    payInfo.payInfo?.expireSecond || ORDER_EXPIRE_TIME
  )

  const { refetchResource, isResourceChange, ResourceChangeModal } =
    useValidResourceChange()

  const screenOrientation = useAtomValue(screenOrientationAtom)

  const [cancelOrderAction] = useCancelOrderMutation()
  const getPaymentDetail = useImperativeQuery<
    GetMirrorDrawOrderQuery,
    GetMirrorDrawOrderQueryVariables
  >(GetMirrorDrawOrderDocument)

  const timerRef = useRef(0)
  const isVideoPortrait = isPortrait && taskType === AiTaskType.VIDEO

  console.log(payInfo, 'payInfo')

  /** 重新生成订单 */
  const onReload = useDebounce(async () => {
    try {
      await createDrawPayOrder({ activeTemplate, printPrice })
      resetSecond(payInfo.payInfo?.expireSecond || ORDER_EXPIRE_TIME)
      startPoll()
    } catch (error: any) {
      if (isResourceChange(error)) {
        await refetchResource()
        setVisible(false)
      } else {
        toast({
          description: graphQLErrorMessage(error) || '下单失败，请稍后重试',
        })
      }
    }
  }, [activeTemplate, printPrice])

  /**
   * 取消订单
   */
  const handleCancelOrder = useDebounce(async () => {
    try {
      await cancelOrderAction({
        variables: { orderId: payInfo.order?.id },
      })
    } catch (error: any) {
      toast({ description: error.message })
    } finally {
      setCanShowVideo(true)
      setVisible(false)
    }
  }, [payInfo])

  const startPoll = () => {
    timerRef.current = window.setInterval(() => {
      getPaymentDetail({ orderId: payInfo.order?.id })
        .then(res => {
          const _status = res.data?.mirrorOrderQuery?.deviceDrawOrder?.status
          if (
            _status &&
            [OrderStatus.PAID, OrderStatus.COMPLETED].includes(_status)
          ) {
            onSuccess?.()
            navigator('/photo')
            setVisible(false)
            window.clearInterval(timerRef.current)
          }
        })
        .catch(error => console.log(error.message))
    }, POLL_TICK * 1000)
  }

  useEffect(() => {
    if (second === 0) {
      window.clearInterval(timerRef.current)
    }
  }, [second])

  useEffect(() => {
    if (!visible) return
    resetSecond(payInfo.payInfo?.expireSecond || ORDER_EXPIRE_TIME)
    startPoll()
    return () => {
      window.clearInterval(timerRef.current)
      clearSecond()
    }
  }, [visible, payInfo])

  return (
    <>
      <MyModal
        open={visible}
        onCancel={() => setVisible(false)}
        width={screenOrientation.isPortrait ? 978 : 1074}
        footer={null}
        className={classNames(
          'h-[768px] p-0 border-none',
          isVideoPortrait && 'h-[896px]'
        )}
        contentClassName={classNames(
          'h-[768px] !p-0 border-none',
          isVideoPortrait && 'h-[896px]'
        )}
        content={
          <div className="w-full h-full flex">
            <div
              className="flex flex-col justify-center w-[486px] h-full bg-white bg-opacity-[0.56] rounded-[32px] bg-no-repeat bg-cover p-[48px]"
              style={{
                backgroundImage: `url(${publicPreLoadSourceObj.blur})`,
              }}
            >
              <div
                className={classNames(
                  'relative flex flex-wrap overflow-hidden w-full  bg-neutral-900 p-[1px] mb-10 rounded-[16px]',
                  [taskType === AiTaskType.VIDEO ? 'h-[270px]' : 'h-[483px]'],
                  isVideoPortrait && 'h-[640px]'
                )}
              >
                {activeTemplate?.exampleImage?.length ? (
                  activeTemplate?.exampleImage?.map((item, index) => (
                    <div key={index} className="w-[50%] h-[240px] p-[1px]">
                      <MyImage
                        className={classNames('w-full h-full', {
                          ['rounded-tl-[16px]']: index === 0,
                          ['rounded-tr-[16px]']: index === 1,
                          ['rounded-bl-[16px]']: index === 2,
                          ['rounded-br-[16px]']: index === 3,
                        })}
                        src={item || ''}
                        tag="v800"
                        imgClassName="object-cover"
                      />
                    </div>
                  ))
                ) : (
                  <MyImage
                    className={classNames('w-full h-full rounded-[16px]')}
                    src={activeTemplate?.image || ''}
                    tag="v800"
                    imgClassName="object-cover"
                  />
                )}
              </div>
              <div
                className={classNames('pb-6  mb-6', {
                  'border-b border-[#C9C5C5]': taskType === AiTaskType.DRAW,
                })}
              >
                <div className="flex justify-between mb-4 leading-[32px]">
                  <div className="text-neutral-100 text-[24px] font-bold opacity-[0.56]">
                    模板名称：
                  </div>
                  <div className="text-neutral-100 text-[24px] font-bold">
                    {activeTemplate?.name}
                  </div>
                </div>
                <div className="flex justify-between leading-[32px]">
                  <div className="text-neutral-100 text-[24px] font-bold opacity-[0.56]">
                    支付价格：
                  </div>
                  <div className="text-neutral-100 text-[24px] font-bold">
                    ¥ {fenToYuan(activeTemplate?.price + printPrice)}
                  </div>
                </div>
              </div>
              {taskType === AiTaskType.DRAW && (
                <div className="text-neutral-100 text-base opacity-[0.56] text-center">
                  {printerEnable &&
                  machineInfo?.printers?.some(
                    it => it.printerStatus === PrinterStatus.AVAILABLE
                  )
                    ? `* 此价格包含 ${imageNum} 张 AI 图片以及 1 张 6寸打印费用`
                    : `* 此价格包含 ${imageNum} 张 AI 图片`}
                </div>
              )}
            </div>
            <div className="flex flex-col items-center justify-center flex-1">
              <div className="text-[32px] font-bold leading-[40px] text-neutral-50 mb-2">
                扫码支付后即可拍照
              </div>
              <div className="text-base text-neutral-400 mb-6">
                本次{taskType === AiTaskType.VIDEO ? '视频' : '图像'}由 AI
                生成，可能与实际有所偏差，请悉知
              </div>
              <div className="relative mb-6 p-6">
                <QRCode
                  value={`${uri}/pay/auth?orderId=${payInfo.order?.id}&orderType=${ORDER_TYPE.DRAW}`}
                  size={338}
                  fgColor="#000"
                  imageSettings={{
                    src: toCDNImage('/images/common/wechat.png'),
                    height: 64,
                    width: 64,
                    excavate: false,
                  }}
                />
                {second === 0 && (
                  <div
                    className="absolute top-0 left-0 w-full h-full z-[100]"
                    onClick={onReload}
                  >
                    <CDNImage src="/images/common/expire_qrcode.png" />
                  </div>
                )}
              </div>
              {second > 0 ? (
                <div className="text-[32px] font-bold leading-[40px] text-neutral-50">
                  <span className="text-gradient-primary">{second}s</span>{' '}
                  后二维码过期
                </div>
              ) : (
                <div className="text-[32px] font-bold leading-[40px] text-neutral-50">
                  二维码已过期，请刷新
                </div>
              )}
            </div>
          </div>
        }
        footerExtra={
          <div className="absolute left-[50%] translate-x-[-50%]  -bottom-[100px]">
            <Button
              variant="outline"
              size="md"
              className="w-[256px] mr-6 text-neutral-900"
              onClick={handleCancelOrder}
            >
              取消支付
            </Button>
          </div>
        }
      />
      <ResourceChangeModal />
    </>
  )
}

export default ModelPaymentModal
