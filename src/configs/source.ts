import { toCDNImage } from '@/utils'

/** public下需要预加载的资源 */
export const publicPreLoadSourceObj = {
  loadingVideo: toCDNImage('/images/video/loading-video-v3.mp4'),
  loadingVideoPoster: toCDNImage('/images/video/loading-video-v3-poster.png'),
  blur: toCDNImage('/images/common/blur.png'),
  correctImage: toCDNImage('/images/photo/correctImage.png'),
  errorImage: toCDNImage('/images/photo/errorImage.png'),
  correctMultipleImage: toCDNImage('/images/photo/correctImage-multiple-2.png'),
  errorMultipleImage: toCDNImage('/images/photo/errorImage-multiple-2.png'),
  enterPicture: toCDNImage('/images/homepage/inter-picture.jpg'),
  enterVideo: toCDNImage('/images/homepage/inter-video-2.mp4'),
  enterVideoPoster: toCDNImage('/images/homepage/inter-video-poster.jpg'),
  silentVerticalImage: toCDNImage('/images/silent/maze_silent_v.webp'),
  silentCrosswiseImage: toCDNImage('/images/silent/maze_silent.webp'),
  silentVerticalBgImg: toCDNImage('/images/silent/maze_silent_vertical.webp'),
  silentCrosswiseBgImg: toCDNImage('/images/silent/maze_silent_crosswise.webp'),
  guideYes: toCDNImage('/images/photo/guide-yes.svg'),
  guideNo: toCDNImage('/images/photo/guide-no.svg'),
  guideLight: toCDNImage('/images/photo/guide-light.svg'),
  shootRetry: toCDNImage('/images/icons/retry.svg'),
  shootConfirm: toCDNImage('/images/icons/confirm.svg'),
  detailPrint: toCDNImage('/images/icons/detail/print.svg'),
  detailMsg: toCDNImage('/images/icons/detail/msg.svg'),
  detailDownload: toCDNImage('/images/icons/detail/download.svg'),
  detailShare: toCDNImage('/images/icons/detail/share.svg'),
  detailEmail: toCDNImage('/images/icons/detail/email.svg'),
  like: toCDNImage('/images/icons/detail/like2.svg'),
  dislike: toCDNImage('/images/icons/detail/dislike.svg'),
  photoMask: toCDNImage('/images/photo/mask.png'),
  photoMaskV: toCDNImage('/images/photo/mask-v.png'),
  iconCorrect: toCDNImage('/images/photo/correct.svg'),
  iconError: toCDNImage('/images/photo/error.svg'),
  female: toCDNImage('/images/icons/female.png'),
  male: toCDNImage('/images/icons/male.png'),
  painting: toCDNImage('/images/common/painting.png'),
}

export const publicPreLoadVideos = [
  publicPreLoadSourceObj.loadingVideo,
  publicPreLoadSourceObj.enterVideo,
  publicPreLoadSourceObj.enterVideoPoster,
]

export const publicPreLoadImages = [
  publicPreLoadSourceObj.loadingVideoPoster,
  publicPreLoadSourceObj.silentVerticalBgImg,
  publicPreLoadSourceObj.silentCrosswiseBgImg,
  publicPreLoadSourceObj.guideYes,
  publicPreLoadSourceObj.guideNo,
  publicPreLoadSourceObj.guideLight,
  publicPreLoadSourceObj.shootRetry,
  publicPreLoadSourceObj.shootConfirm,
  publicPreLoadSourceObj.detailPrint,
  publicPreLoadSourceObj.detailMsg,
  publicPreLoadSourceObj.detailDownload,
  publicPreLoadSourceObj.detailShare,
  publicPreLoadSourceObj.detailEmail,
  publicPreLoadSourceObj.like,
  publicPreLoadSourceObj.dislike,
  publicPreLoadSourceObj.photoMask,
  publicPreLoadSourceObj.photoMaskV,
  publicPreLoadSourceObj.iconCorrect,
  publicPreLoadSourceObj.iconError,
  publicPreLoadSourceObj.female,
  publicPreLoadSourceObj.male,
  publicPreLoadSourceObj.painting,
  publicPreLoadSourceObj.silentVerticalBgImg,
  publicPreLoadSourceObj.silentCrosswiseBgImg,
]
